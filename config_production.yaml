# AUTONOMOUS BYBIT TRADING BOT - PRODUCTION CONFIGURATION
# REAL MONEY TRADING - MAX<PERSON>UM PROFIT OPTIMIZATION
# NO TESTNET, NO SIMULATION, NO PAPER TRADING

# ================================
# SYSTEM CONFIGURATION - PRODUCTION
# ================================
system:
  name: "Autonomous Bybit Trading Bot - Production"
  version: "2.0.0"
  environment: "production"
  mode: "live_trading"
  debug_mode: false
  auto_start: true
  self_healing: true
  autonomous_mode: true
  profit_maximization: true

# ================================
# TRADING CONFIGURATION - PRODUCTION
# ================================
trading:
  # Exchange Settings - PRODUCTION MAINNET ONLY
  exchange: "bybit"
  environment: "mainnet"
  paper_trading: false  # DISABLED
  live_trading: true    # ENABLED
  real_money_mode: true

  # Risk Management - OPTIMIZED FOR PROFIT
  max_risk_per_trade: 0.05      # 5% max risk per trade
  max_daily_loss: 0.15          # 15% max daily loss
  max_position_size: 0.50       # 50% max position size
  leverage_range: [1, 20]       # Higher leverage for profit maximization
  stop_loss_percentage: 0.03    # 3% stop loss
  take_profit_percentage: 0.08  # 8% take profit

  # Trading Pairs - EXPANDED FOR MAXIMUM OPPORTUNITIES
  trading_pairs:
    - "BTCUSDT"
    - "ETHUSDT"
    - "ADAUSDT"
    - "SOLUSDT"
    - "DOTUSDT"
    - "BNBUSDT"
    - "XRPUSDT"
    - "MATICUSDT"
    - "AVAXUSDT"
    - "LINKUSDT"
    - "UNIUSDT"
    - "LTCUSDT"

  # Strategy Configuration - PROFIT OPTIMIZED
  strategies:
    momentum:
      enabled: true
      weight: 0.25
      lookback_period: 14
      rsi_threshold: [25, 75]

    mean_reversion:
      enabled: true
      weight: 0.25
      bollinger_period: 20
      bollinger_std: 2.0

    trend_following:
      enabled: true
      weight: 0.30
      ema_short: 12
      ema_long: 26
      signal_period: 9

    arbitrage:
      enabled: true
      weight: 0.20
      min_profit_threshold: 0.003
      max_execution_time: 5

  # Execution Settings - OPTIMIZED FOR SPEED
  order_timeout: 15
  slippage_tolerance: 0.003
  trading_cycle_interval: 10
  max_concurrent_orders: 10
  order_retry_attempts: 3
  position_update_interval: 5

# ================================
# API CREDENTIALS - PRODUCTION MAINNET
# ================================
api_keys:
  # Bybit API Keys - PRODUCTION ONLY
  bybit:
    api_key: "WbQDRvmESPfUGgXQEj"
    api_secret: "vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga"
    testnet: false
    base_url: "https://api.bybit.com"
    websocket_url: "wss://stream.bybit.com/v5/public/linear"
    recv_window: 5000

  # External API Keys for Data Intelligence
  newsapi:
    api_key: "your_news_api_key_here"

  alpha_vantage:
    api_key: "your_alpha_vantage_key_here"

  fred:
    api_key: "your_fred_api_key_here"

  twitter:
    bearer_token: "AAAAAAAAAAAAAAAAAAAAACPo1AEAAAAA%2FkHMV0BXzYlGb9U3N2MwD5gfnAU%3D56Ql9TOVDAQxhJVAUuYX6TOzBUwjok1niLnHRHnkpmqbnQu6pW"
    api_key: "your_twitter_api_key_here"
    api_secret: "your_twitter_api_secret_here"
    access_token: "your_twitter_access_token_here"
    access_token_secret: "your_twitter_access_token_secret_here"

  reddit:
    client_id: "your_reddit_client_id_here"
    client_secret: "your_reddit_client_secret_here"
    user_agent: "BybitBot/1.0"

# ================================
# DATABASE CONFIGURATION - PRODUCTION
# ================================
database:
  url: "postgresql://postgres:password@localhost:5432/bybit_trading_bot"
  host: "localhost"
  port: 5432
  database: "bybit_trading_bot"
  user: "postgres"
  password: "password"
  pool_size: 30
  max_overflow: 100
  pool_timeout: 30
  echo: false
  ssl_mode: "prefer"

# ================================
# HARDWARE MONITORING - PRODUCTION
# ================================
hardware:
  check_interval: 30
  cpu_temp_threshold: 75
  memory_threshold: 80
  disk_threshold: 85
  auto_shutdown_on_critical: true
  performance_monitoring: true
  resource_optimization: true

# ================================
# DATA CRAWLER CONFIGURATION - PRODUCTION
# ================================
data_crawler:
  # Update intervals - OPTIMIZED FOR REAL-TIME DATA
  market_data_interval: 5       # 5 seconds for real-time data
  news_interval: 60             # 1 minute for news
  social_sentiment_interval: 120 # 2 minutes for social data
  economic_data_interval: 1800  # 30 minutes for economic data

  # Data retention
  data_retention_days: 365      # 1 year for production

  # News sources - COMPREHENSIVE
  news_sources:
    - "coindesk"
    - "cointelegraph"
    - "crypto-news"
    - "bitcoin-magazine"
    - "decrypt"
    - "the-block"
    - "coinbase-blog"

  # Social media keywords - EXPANDED
  social_keywords:
    - "bitcoin"
    - "ethereum"
    - "crypto"
    - "btc"
    - "eth"
    - "defi"
    - "nft"
    - "trading"
    - "pump"
    - "dump"
    - "moon"
    - "bear"
    - "bull"

  # Economic indicators
  economic_indicators:
    - "GDP"
    - "INFLATION"
    - "UNEMPLOYMENT"
    - "INTEREST_RATES"
    - "DXY"
    - "VIX"
    - "GOLD"
    - "OIL"

# ================================
# MACHINE LEARNING CONFIGURATION - PRODUCTION
# ================================
ml:
  # Model settings - OPTIMIZED FOR PRODUCTION
  retrain_interval: 3600        # 1 hour retraining
  prediction_horizon: 300       # 5 minutes
  feature_lookback: 200         # More historical data
  model_ensemble: true          # Use multiple models

  # Model parameters - ENHANCED
  models:
    xgboost:
      n_estimators: 200
      max_depth: 8
      learning_rate: 0.05
      subsample: 0.8

    lstm:
      sequence_length: 100
      hidden_units: 128
      dropout: 0.3
      epochs: 100
      batch_size: 64

    transformer:
      enabled: true
      attention_heads: 8
      hidden_size: 256
      num_layers: 6

  # Feature engineering - COMPREHENSIVE
  features:
    technical_indicators: true
    sentiment_scores: true
    economic_data: true
    volume_profile: true
    order_book_imbalance: true
    market_microstructure: true
    cross_asset_correlations: true
    volatility_surface: true

# ================================
# LOGGING CONFIGURATION - PRODUCTION
# ================================
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "E:/bybit_bot_logs/trading_bot_production.log"
  max_file_size: "50MB"
  backup_count: 10
  rotation: "daily"
  compression: true

# ================================
# API SERVER CONFIGURATION - PRODUCTION
# ================================
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  workers: 4
  timeout: 60
  max_connections: 1000

# ================================
# SECURITY SETTINGS - PRODUCTION
# ================================
security:
  jwt_secret: "bybit-trading-bot-super-secure-secret-key-2024-production"
  jwt_expiry_hours: 24
  rate_limit_per_minute: 120
  encryption_enabled: true
  audit_logging: true
  ip_whitelist_enabled: false

# ================================
# NOTIFICATION SETTINGS - PRODUCTION
# ================================
notifications:
  enabled: true
  channels:
    email:
      enabled: false

    webhook:
      enabled: true
      url: "your_webhook_url_here"

    telegram:
      enabled: true
      bot_token: "your_telegram_bot_token_here"
      chat_id: "your_telegram_chat_id_here"

  # Notification triggers - PROFIT FOCUSED
  triggers:
    large_profit: 0.03          # 3% profit
    large_loss: -0.02           # 2% loss
    system_error: true
    hardware_warning: true
    daily_summary: true
    trade_execution: true
    position_opened: true
    position_closed: true

# ================================
# SUPERGPT CONFIGURATION - PRODUCTION
# ================================
supergpt:
  enabled: true
  auto_learning: true
  self_improvement: true
  meta_cognition: true
  advanced_reasoning: true

  capabilities:
    natural_language_processing: true
    code_generation: true
    strategy_optimization: true
    market_analysis: true
    risk_assessment: true
    performance_prediction: true
    anomaly_detection: true
    adaptive_learning: true

  models:
    primary: "gpt-4-turbo"
    fallback: "gpt-3.5-turbo"

  learning:
    continuous_learning: true
    experience_replay: true
    meta_learning: true
    transfer_learning: true
    reinforcement_learning: true

# ================================
# PERFORMANCE OPTIMIZATION - PRODUCTION
# ================================
performance:
  optimization_enabled: true
  auto_scaling: true
  resource_monitoring: true
  performance_tuning: true
  cache_enabled: true
  parallel_processing: true
  gpu_acceleration: false
  memory_optimization: true

# ================================
# BACKUP AND RECOVERY - PRODUCTION
# ================================
backup:
  enabled: true
  interval: 3600                # 1 hour
  retention_days: 30
  backup_location: "E:/bybit_bot_backups"
  compression: true
  encryption: true

# ================================
# MONITORING AND ALERTING - PRODUCTION
# ================================
monitoring:
  enabled: true
  metrics_collection: true
  performance_tracking: true
  error_tracking: true
  uptime_monitoring: true
  resource_monitoring: true
  trade_monitoring: true
  profit_tracking: true