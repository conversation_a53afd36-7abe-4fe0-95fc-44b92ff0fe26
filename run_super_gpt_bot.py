"""
Complete Setup Script for Super GPT Bybit Bot
Initializes database, integrates all systems, and starts the bot
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add the bot directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
from bybit_bot.core.bot_manager import Bot<PERSON>anager
from bybit_bot.ai.memory_manager import PersistentMemoryManager
from bybit_bot.core.time_manager import EnhancedTimeManager
from bybit_bot.data_crawler.firecrawl_collector import FirecrawlDataCollector
from bybit_bot.data_crawler.huggingface_integration import integrate_huggingface_data

logger = logging.getLogger("super_gpt_bybit_bot.setup")


class SuperGPTBotSetup:
    """Complete setup and initialization for the Super GPT Bybit Bot"""
    
    def __init__(self):
        self.config = None
        self.db = None
        self.bot_manager = None
        self.memory_manager = None
        self.time_manager = None
        self.firecrawl_collector = None
        self.hardware_monitor = None
        self.setup_complete = False
        
    async def run_complete_setup(self):
        """Run the complete setup process"""
        try:
            print("🚀 Starting Super GPT Bybit Bot Complete Setup...")
            
            # Step 1: Initialize configuration
            await self._initialize_config()
            
            # Step 2: Setup database and schema
            await self._setup_database()
            
            # Step 3: Initialize core components
            await self._initialize_components()
            
            # Step 4: Setup AI and memory systems
            await self._setup_ai_systems()
            
            # Step 5: Integrate external data sources
            await self._integrate_data_sources()
            
            # Step 6: Initialize bot manager
            await self._initialize_bot_manager()
            
            # Step 7: Run system health check
            await self._run_system_health_check()
            
            # Step 8: Start the bot
            await self._start_bot()
            
            print("✅ Super GPT Bybit Bot setup completed successfully!")
            self.setup_complete = True
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            print(f"❌ Setup failed: {e}")
            raise
    
    async def _initialize_config(self):
        """Initialize bot configuration"""
        try:
            print("📋 Initializing configuration...")
            
            # Load configuration
            self.config = BotConfig()
            
            # Setup logging
            self.logger = TradingBotLogger(self.config)
            
            logger.info("✅ Configuration initialized")
            print("✅ Configuration loaded")
            
        except Exception as e:
            logger.error(f"Error initializing config: {e}")
            raise
    
    async def _setup_database(self):
        """Setup database connection and schema"""
        try:
            print("🗄️ Setting up database...")
            
            # Initialize database manager
            self.db = DatabaseManager(self.config)
            await self.db.connect()
            
            # Run database schema creation
            schema_file = Path(__file__).parent / "database_schema_complete.sql"
            if schema_file.exists():
                print("📄 Executing database schema...")
                
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # Execute the schema (split by statements if needed)
                try:
                    await self.db.execute_raw(schema_sql)
                    logger.info("✅ Database schema created successfully")
                    print("✅ Database schema created")
                except Exception as e:
                    logger.warning(f"Schema execution completed with warnings: {e}")
                    print(f"⚠️ Schema executed with warnings: {e}")
            else:
                logger.warning("Schema file not found, using existing database")
                print("⚠️ Schema file not found, using existing database")
            
        except Exception as e:
            logger.error(f"Error setting up database: {e}")
            raise
    
    async def _initialize_components(self):
        """Initialize core bot components"""
        try:
            print("🔧 Initializing core components...")
            
            # Initialize hardware monitor
            self.hardware_monitor = HardwareMonitor(self.config)
            await self.hardware_monitor.start()
            
            logger.info("✅ Core components initialized")
            print("✅ Core components ready")
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            raise
    
    async def _setup_ai_systems(self):
        """Setup AI and memory management systems"""
        try:
            print("🧠 Setting up AI systems...")
            
            # Initialize memory manager
            self.memory_manager = PersistentMemoryManager(self.db)
            await self.memory_manager.initialize()
            
            # Initialize enhanced time manager
            self.time_manager = EnhancedTimeManager()
            await self.time_manager.initialize()
            
            logger.info("✅ AI systems initialized")
            print("✅ AI systems ready")
            
        except Exception as e:
            logger.error(f"Error setting up AI systems: {e}")
            raise
    
    async def _integrate_data_sources(self):
        """Integrate external data sources"""
        try:
            print("🌐 Integrating external data sources...")
            
            # Initialize Firecrawl data collector
            firecrawl_api_key = "fc-611319452f0e4e1db2197b70078df8ad"  # From user's request
            self.firecrawl_collector = FirecrawlDataCollector(
                api_key=firecrawl_api_key,
                database_manager=self.db
            )
            await self.firecrawl_collector.initialize()
            
            # Integrate HuggingFace data
            print("📊 Integrating HuggingFace datasets...")
            hf_result = await integrate_huggingface_data(self.db)
            logger.info(f"HuggingFace integration: {hf_result}")
            
            logger.info("✅ External data sources integrated")
            print("✅ Data sources integrated")
            
        except Exception as e:
            logger.error(f"Error integrating data sources: {e}")
            print(f"⚠️ Data source integration completed with warnings: {e}")
            # Continue setup even if data integration has issues
    
    async def _initialize_bot_manager(self):
        """Initialize the main bot manager"""
        try:
            print("🤖 Initializing bot manager...")
            
            # Create bot manager with all components
            self.bot_manager = BotManager(
                config=self.config,
                database_manager=self.db,
                hardware_monitor=self.hardware_monitor
            )
            
            # Initialize the bot manager
            await self.bot_manager.initialize()
            
            logger.info("✅ Bot manager initialized")
            print("✅ Bot manager ready")
            
        except Exception as e:
            logger.error(f"Error initializing bot manager: {e}")
            raise
    
    async def _run_system_health_check(self):
        """Run comprehensive system health check"""
        try:
            print("🏥 Running system health check...")
            
            health_status = {
                'database': False,
                'memory_manager': False,
                'time_manager': False,
                'firecrawl': False,
                'bot_manager': False,
                'hardware_monitor': False
            }
            
            # Check database
            try:
                await self.db.fetch_one("SELECT 1 as test")
                health_status['database'] = True
            except Exception as e:
                logger.error(f"Database health check failed: {e}")
            
            # Check memory manager
            try:
                if self.memory_manager and hasattr(self.memory_manager, 'is_initialized'):
                    health_status['memory_manager'] = True
            except Exception as e:
                logger.error(f"Memory manager health check failed: {e}")
            
            # Check time manager
            try:
                if self.time_manager:
                    current_time = await self.time_manager.get_current_time()
                    health_status['time_manager'] = current_time is not None
            except Exception as e:
                logger.error(f"Time manager health check failed: {e}")
            
            # Check firecrawl
            try:
                if self.firecrawl_collector:
                    health_status['firecrawl'] = True
            except Exception as e:
                logger.error(f"Firecrawl health check failed: {e}")
            
            # Check bot manager
            try:
                if self.bot_manager and hasattr(self.bot_manager, 'is_initialized'):
                    health_status['bot_manager'] = self.bot_manager.is_initialized
            except Exception as e:
                logger.error(f"Bot manager health check failed: {e}")
            
            # Check hardware monitor
            try:
                if self.hardware_monitor:
                    health_status['hardware_monitor'] = True
            except Exception as e:
                logger.error(f"Hardware monitor health check failed: {e}")
            
            # Print health status
            print("🏥 System Health Status:")
            for component, status in health_status.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {component}: {'OK' if status else 'FAILED'}")
            
            healthy_components = sum(health_status.values())
            total_components = len(health_status)
            
            if healthy_components >= total_components * 0.8:  # 80% healthy
                logger.info(f"✅ System health check passed: {healthy_components}/{total_components} components healthy")
                print(f"✅ System health: {healthy_components}/{total_components} components OK")
            else:
                logger.warning(f"⚠️ System health check concerns: {healthy_components}/{total_components} components healthy")
                print(f"⚠️ System health: {healthy_components}/{total_components} components OK")
            
        except Exception as e:
            logger.error(f"Error running health check: {e}")
            print(f"❌ Health check failed: {e}")
    
    async def _start_bot(self):
        """Start the bot"""
        try:
            print("🚀 Starting Super GPT Bybit Bot...")
            
            if not self.bot_manager:
                raise Exception("Bot manager not initialized")
            
            # Start the bot
            await self.bot_manager.start()
            
            logger.info("🚀 Super GPT Bybit Bot started successfully!")
            print("🚀 Bot started successfully!")
            
            # Keep the bot running
            try:
                print("🔄 Bot is now running... Press Ctrl+C to stop")
                while self.bot_manager.is_trading:
                    await asyncio.sleep(10)  # Check every 10 seconds
                    
                    # Optional: Print status every 5 minutes
                    if hasattr(self.bot_manager, 'cycle_count') and self.bot_manager.cycle_count % 30 == 0:
                        status = await self.bot_manager.get_status()
                        print(f"📊 Bot Status - Trades: {status.get('trading_metrics', {}).get('total_trades', 0)}, "
                              f"P&L: ${status.get('trading_metrics', {}).get('total_pnl', 0):.2f}")
                        
            except KeyboardInterrupt:
                print("\n⏹️ Shutting down bot...")
                await self.bot_manager.shutdown()
                print("✅ Bot shutdown complete")
                
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            print(f"❌ Failed to start bot: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.bot_manager:
                await self.bot_manager.shutdown()
            
            if self.hardware_monitor:
                await self.hardware_monitor.stop()
            
            if self.db:
                await self.db.close()
                
            logger.info("✅ Cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


async def main():
    """Main entry point for the Super GPT Bybit Bot"""
    setup = SuperGPTBotSetup()
    
    try:
        await setup.run_complete_setup()
        
    except KeyboardInterrupt:
        print("\n⏹️ Setup interrupted by user")
    except Exception as e:
        print(f"❌ Setup failed: {e}")
    finally:
        await setup.cleanup()


if __name__ == "__main__":
    print("🚀 Super GPT Bybit Bot - Complete Setup & Launch")
    print("=" * 50)
    
    # Run the setup
    asyncio.run(main())
