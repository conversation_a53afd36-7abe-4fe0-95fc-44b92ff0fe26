#!/usr/bin/env python3
"""
Super-GPT Autonomous Trading Bot - Main Entry Point

A fully autonomous trading bot with advanced AI capabilities, self-healing mechanisms,
meta-learning, and real-time adaptation for cryptocurrency trading.

Features:
- Multi-agent orchestration
- Self-healing and error recovery
- Meta-learning and adaptation
- Real-time code optimization
- Autonomous decision-making
- Advanced risk management
- Continuous learning and improvement
"""

import asyncio
import logging
import signal
import sys
import threading
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import os
from pathlib import Path

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import pandas as pd
import numpy as np

# Import Super-GPT components
from bybit_bot.core.bot_manager import BotManager
from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import setup_logging, TradingBotLogger
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.ai.memory_manager import PersistentMemoryManager
from bybit_bot.agents.agent_orchestrator import AgentOrchestrator
from bybit_bot.agents.learning_agent import LearningAgent
from bybit_bot.risk.advanced_risk_manager import AdvancedRiskManager
from bybit_bot.core.enhanced_time_manager import EnhancedTimeManager
from bybit_bot.data_integration.huggingface_manager import HuggingFaceManager
from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
from bybit_bot.analytics.performance_analyzer import PerformanceAnalyzer
from bybit_bot.ml.market_predictor import MarketPredictor
from bybit_bot.core.self_healing import SelfHealingSystem
from bybit_bot.ai.meta_learner import MetaLearner
from bybit_bot.core.code_optimizer import CodeOptimizer
from bybit_bot.ai.model_selector import ModelSelector
from bybit_bot.core.autonomy_engine import AutonomyEngine

# Data collection components
from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
from bybit_bot.strategies.adaptive_strategy_engine import AdaptiveStrategyEngine

# Global components
bot_manager: Optional[BotManager] = None
database_manager: Optional[DatabaseManager] = None
memory_manager: Optional[PersistentMemoryManager] = None
agent_orchestrator: Optional[AgentOrchestrator] = None
learning_agent: Optional[LearningAgent] = None
risk_manager: Optional[AdvancedRiskManager] = None
time_manager: Optional[EnhancedTimeManager] = None
huggingface_manager: Optional[HuggingFaceManager] = None
hardware_monitor: Optional[HardwareMonitor] = None
performance_analyzer: Optional[PerformanceAnalyzer] = None
market_predictor: Optional[MarketPredictor] = None
self_healing_system: Optional[SelfHealingSystem] = None
meta_learner: Optional[MetaLearner] = None
code_optimizer: Optional[CodeOptimizer] = None
model_selector: Optional[ModelSelector] = None
autonomy_engine: Optional[AutonomyEngine] = None

# Data collection components
market_data_crawler: Optional[MarketDataCrawler] = None
news_sentiment_crawler: Optional[NewsSentimentCrawler] = None
social_sentiment_crawler: Optional[SocialSentimentCrawler] = None
economic_data_crawler: Optional[EconomicDataCrawler] = None
adaptive_strategy_engine: Optional[AdaptiveStrategyEngine] = None

is_shutting_down = False

# FastAPI app
app = FastAPI(
    title="Super-GPT Autonomous Trading Bot",
    description="Fully autonomous trading bot with advanced AI, self-healing, and meta-learning capabilities",
    version="3.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class SuperGPTTradingService:
    """
    Super-GPT Trading Service
    
    Orchestrates all components of the autonomous trading bot
    """
    
    def __init__(self):
        self.config = BotConfig()
        self.logger = TradingBotLogger("SuperGPTTradingService")
        self.running = False
        self.initialized = False
        self.service_health = {}
        
        # Performance metrics
        self.metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'uptime': 0.0,
            'autonomy_level': 'semi_autonomous',
            'last_update': datetime.utcnow()
        }
    
    async def initialize(self):
        """Initialize all Super-GPT components"""
        try:
            self.logger.info("Initializing Super-GPT Trading Service")
            
            # Initialize core components
            await self._initialize_core_components()
            
            # Initialize AI components
            await self._initialize_ai_components()
            
            # Initialize data components
            await self._initialize_data_components()
            
            # Initialize monitoring components
            await self._initialize_monitoring_components()
            
            # Initialize agents
            await self._initialize_agents()
            
            # Initialize autonomy engine
            await self._initialize_autonomy_engine()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.initialized = True
            self.logger.info("Super-GPT Trading Service initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Super-GPT Trading Service: {e}")
            raise
    
    async def _initialize_core_components(self):
        """Initialize core system components"""
        global database_manager, memory_manager, risk_manager, time_manager
        
        # Database manager
        database_manager = DatabaseManager(self.config)
        await database_manager.initialize()
        
        # Memory manager
        memory_manager = PersistentMemoryManager(self.config, database_manager)
        await memory_manager.initialize()
        
        # Risk manager
        risk_manager = AdvancedRiskManager(self.config, database_manager)
        await risk_manager.initialize()
        
        # Time manager
        time_manager = EnhancedTimeManager(self.config)
        await time_manager.initialize()
        
        self.logger.info("Core components initialized")
    
    async def _initialize_ai_components(self):
        """Initialize AI and ML components"""
        global market_predictor, self_healing_system, meta_learner, code_optimizer, model_selector
        
        # Market predictor
        market_predictor = MarketPredictor(self.config, database_manager)
        await market_predictor.initialize()
        
        # Self-healing system
        self_healing_system = SelfHealingSystem(self.config, database_manager)
        await self_healing_system.initialize()
        
        # Meta-learner
        meta_learner = MetaLearner(self.config, database_manager, memory_manager)
        await meta_learner.initialize()
        
        # Code optimizer
        code_optimizer = CodeOptimizer(self.config, database_manager)
        await code_optimizer.initialize()
        
        # Model selector
        model_selector = ModelSelector(self.config, database_manager)
        await model_selector.initialize()
        
        self.logger.info("AI components initialized")
    
    async def _initialize_data_components(self):
        """Initialize data collection components"""
        global market_data_crawler, news_sentiment_crawler, social_sentiment_crawler, economic_data_crawler, huggingface_manager
        
        # Market data crawler
        market_data_crawler = MarketDataCrawler(self.config, database_manager)
        await market_data_crawler.initialize()
        
        # News sentiment crawler
        news_sentiment_crawler = NewsSentimentCrawler(self.config, database_manager)
        await news_sentiment_crawler.initialize()
        
        # Social sentiment crawler
        social_sentiment_crawler = SocialSentimentCrawler(self.config, database_manager)
        await social_sentiment_crawler.initialize()
        
        # Economic data crawler
        economic_data_crawler = EconomicDataCrawler(self.config, database_manager)
        await economic_data_crawler.initialize()
        
        # HuggingFace manager
        huggingface_manager = HuggingFaceManager(self.config, database_manager)
        await huggingface_manager.initialize()
        
        self.logger.info("Data components initialized")
    
    async def _initialize_monitoring_components(self):
        """Initialize monitoring components"""
        global hardware_monitor, performance_analyzer
        
        # Hardware monitor
        hardware_monitor = HardwareMonitor(self.config)
        await hardware_monitor.initialize()
        
        # Performance analyzer
        performance_analyzer = PerformanceAnalyzer(self.config, database_manager)
        await performance_analyzer.initialize()
        
        self.logger.info("Monitoring components initialized")
    
    async def _initialize_agents(self):
        """Initialize agent system"""
        global agent_orchestrator, learning_agent, bot_manager, adaptive_strategy_engine
        
        # Learning agent
        learning_agent = LearningAgent(
            self.config, 
            database_manager, 
            memory_manager,
            market_predictor,
            meta_learner
        )
        await learning_agent.initialize()
        
        # Agent orchestrator
        agent_orchestrator = AgentOrchestrator(
            self.config, 
            database_manager,
            memory_manager,
            risk_manager,
            time_manager,
            learning_agent
        )
        await agent_orchestrator.initialize()
        
        # Bot manager
        bot_manager = BotManager(
            self.config,
            database_manager,
            risk_manager,
            agent_orchestrator
        )
        await bot_manager.initialize()
        
        # Adaptive strategy engine
        adaptive_strategy_engine = AdaptiveStrategyEngine(
            self.config,
            database_manager,
            memory_manager,
            market_predictor,
            meta_learner
        )
        await adaptive_strategy_engine.initialize()
        
        self.logger.info("Agent system initialized")
    
    async def _initialize_autonomy_engine(self):
        """Initialize autonomy engine"""
        global autonomy_engine
        
        autonomy_engine = AutonomyEngine(
            self.config,
            database_manager,
            time_manager,
            memory_manager,
            risk_manager,
            agent_orchestrator
        )
        await autonomy_engine.initialize()
        
        self.logger.info("Autonomy engine initialized")
    
    async def _start_background_tasks(self):
        """Start background tasks"""
        # Start system health monitoring
        asyncio.create_task(self._system_health_loop())
        
        # Start performance monitoring
        asyncio.create_task(self._performance_monitoring_loop())
        
        # Start self-healing monitoring
        asyncio.create_task(self._self_healing_loop())
        
        # Start meta-learning loop
        asyncio.create_task(self._meta_learning_loop())
        
        # Start code optimization loop
        asyncio.create_task(self._code_optimization_loop())
        
        self.logger.info("Background tasks started")
    
    async def start_trading(self):
        """Start autonomous trading"""
        try:
            if not self.initialized:
                await self.initialize()
            
            self.logger.info("Starting autonomous trading")
            self.running = True
            
            # Start main trading loop
            asyncio.create_task(self._main_trading_loop())
            
            # Start decision-making loop
            asyncio.create_task(self._decision_making_loop())
            
            self.logger.info("Autonomous trading started")
            
        except Exception as e:
            self.logger.error(f"Failed to start trading: {e}")
            raise
    
    async def stop_trading(self):
        """Stop autonomous trading"""
        try:
            self.logger.info("Stopping autonomous trading")
            self.running = False
            
            # Shutdown all components
            if autonomy_engine:
                await autonomy_engine.shutdown()
            
            if agent_orchestrator:
                await agent_orchestrator.shutdown()
            
            if bot_manager:
                await bot_manager.shutdown()
            
            if self_healing_system:
                await self_healing_system.shutdown()
            
            if meta_learner:
                await meta_learner.shutdown()
            
            if code_optimizer:
                await code_optimizer.shutdown()
            
            if model_selector:
                await model_selector.shutdown()
            
            if database_manager:
                await database_manager.close()
            
            self.logger.info("Autonomous trading stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping trading: {e}")
    
    async def _main_trading_loop(self):
        """Main trading loop"""
        cycle_count = 0
        
        while self.running:
            try:
                cycle_count += 1
                self.logger.info(f"Starting trading cycle {cycle_count}")
                
                # Gather market intelligence
                market_intelligence = await self._gather_market_intelligence()
                
                # Let autonomy engine make decisions
                if autonomy_engine:
                    await autonomy_engine.make_decision(
                        DecisionType.STRATEGIC,
                        DecisionContext.MARKET_OPENING,
                        market_intelligence
                    )
                
                # Execute trading through bot manager
                if bot_manager:
                    await bot_manager.execute_trading_cycle(market_intelligence)
                
                # Learn from results
                if learning_agent:
                    await learning_agent.learn_from_cycle(market_intelligence)
                
                # Update metrics
                await self._update_metrics()
                
                # Sleep between cycles
                await asyncio.sleep(self.config.trading_interval)
                
            except Exception as e:
                self.logger.error(f"Error in trading cycle: {e}")
                
                # Try self-healing
                if self_healing_system:
                    await self_healing_system.handle_error(e, "trading_loop")
                
                await asyncio.sleep(60)  # Wait before retry
    
    async def _decision_making_loop(self):
        """Decision-making loop"""
        while self.running:
            try:
                # Let autonomy engine evaluate and make decisions
                if autonomy_engine:
                    # Check for decision opportunities
                    await autonomy_engine.achieve_goal("profit_maximization")
                    await autonomy_engine.achieve_goal("risk_management")
                    await autonomy_engine.achieve_goal("continuous_learning")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in decision-making loop: {e}")
                await asyncio.sleep(60)
    
    async def _gather_market_intelligence(self) -> Dict[str, Any]:
        """Gather comprehensive market intelligence"""
        intelligence = {
            "timestamp": datetime.utcnow(),
            "market_data": {},
            "sentiment": {},
            "economic_indicators": {},
            "technical_indicators": {},
            "predictions": {},
            "risk_assessment": {}
        }
        
        try:
            # Get market data
            if market_data_crawler:
                for symbol in self.config.get_trading_pairs():
                    market_data = await market_data_crawler.get_latest_market_data(symbol)
                    if market_data:
                        intelligence["market_data"][symbol] = market_data
            
            # Get sentiment data
            if news_sentiment_crawler:
                intelligence["sentiment"] = await news_sentiment_crawler.get_sentiment_summary(6)
            
            # Get predictions
            if market_predictor:
                for symbol in self.config.get_trading_pairs():
                    predictions = await market_predictor.get_predictions(symbol)
                    if predictions:
                        intelligence["predictions"][symbol] = predictions
            
            # Get risk assessment
            if risk_manager:
                intelligence["risk_assessment"] = await risk_manager.get_risk_assessment()
            
        except Exception as e:
            self.logger.error(f"Error gathering market intelligence: {e}")
        
        return intelligence
    
    async def _system_health_loop(self):
        """System health monitoring loop"""
        while self.running:
            try:
                # Monitor system health
                if hardware_monitor:
                    health = await hardware_monitor.get_health_metrics()
                    self.service_health['hardware'] = health
                
                # Monitor component health
                await self._check_component_health()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in system health loop: {e}")
                await asyncio.sleep(120)
    
    async def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        while self.running:
            try:
                # Analyze performance
                if performance_analyzer:
                    performance = await performance_analyzer.analyze_performance()
                    self.metrics.update(performance)
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in performance monitoring loop: {e}")
                await asyncio.sleep(600)
    
    async def _self_healing_loop(self):
        """Self-healing monitoring loop"""
        while self.running:
            try:
                # Check for issues and heal
                if self_healing_system:
                    await self_healing_system.monitor_and_heal()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in self-healing loop: {e}")
                await asyncio.sleep(60)
    
    async def _meta_learning_loop(self):
        """Meta-learning loop"""
        while self.running:
            try:
                # Perform meta-learning
                if meta_learner:
                    await meta_learner.meta_learn()
                
                await asyncio.sleep(3600)  # Every hour
                
            except Exception as e:
                self.logger.error(f"Error in meta-learning loop: {e}")
                await asyncio.sleep(3600)
    
    async def _code_optimization_loop(self):
        """Code optimization loop"""
        while self.running:
            try:
                # Optimize code
                if code_optimizer:
                    await code_optimizer.optimize_system()
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Error in code optimization loop: {e}")
                await asyncio.sleep(1800)
    
    async def _check_component_health(self):
        """Check health of all components"""
        components = {
            'database': database_manager,
            'memory': memory_manager,
            'risk_manager': risk_manager,
            'bot_manager': bot_manager,
            'agent_orchestrator': agent_orchestrator,
            'autonomy_engine': autonomy_engine,
            'self_healing': self_healing_system,
            'meta_learner': meta_learner,
            'code_optimizer': code_optimizer,
            'model_selector': model_selector
        }
        
        for name, component in components.items():
            try:
                if component and hasattr(component, 'get_health_status'):
                    health = await component.get_health_status()
                    self.service_health[name] = health
                else:
                    self.service_health[name] = {'status': 'unknown'}
            except Exception as e:
                self.service_health[name] = {'status': 'error', 'error': str(e)}
    
    async def _update_metrics(self):
        """Update service metrics"""
        try:
            if bot_manager:
                bot_metrics = await bot_manager.get_performance_metrics()
                self.metrics.update(bot_metrics)
            
            if autonomy_engine:
                autonomy_status = await autonomy_engine.get_autonomy_status()
                self.metrics['autonomy_level'] = autonomy_status.get('autonomy_level', 'unknown')
            
            self.metrics['last_update'] = datetime.utcnow()
            
        except Exception as e:
            self.logger.error(f"Error updating metrics: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive service status"""
        return {
            'service': 'Super-GPT Trading Bot',
            'version': '3.0.0',
            'running': self.running,
            'initialized': self.initialized,
            'metrics': self.metrics,
            'health': self.service_health,
            'components': {
                'database': database_manager is not None,
                'memory': memory_manager is not None,
                'risk_manager': risk_manager is not None,
                'bot_manager': bot_manager is not None,
                'agent_orchestrator': agent_orchestrator is not None,
                'autonomy_engine': autonomy_engine is not None,
                'self_healing': self_healing_system is not None,
                'meta_learner': meta_learner is not None,
                'code_optimizer': code_optimizer is not None,
                'model_selector': model_selector is not None
            }
        }


# Global trading service
trading_service = SuperGPTTradingService()


# API Routes
@app.get("/")
async def root():
    """Root endpoint with bot status"""
    return {
        "message": "Super-GPT Autonomous Trading Bot",
        "description": "Fully autonomous trading bot with advanced AI capabilities",
        "version": "3.0.0",
        "status": "running" if trading_service.running else "stopped",
        "timestamp": datetime.utcnow().isoformat(),
        "features": [
            "Multi-agent orchestration",
            "Self-healing and error recovery",
            "Meta-learning and adaptation",
            "Real-time code optimization",
            "Autonomous decision-making",
            "Advanced risk management",
            "Continuous learning",
            "Real-time market intelligence",
            "Advanced sentiment analysis",
            "Automated portfolio optimization"
        ]
    }

@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    return trading_service.get_status()

@app.get("/metrics")
async def get_metrics():
    """Get performance metrics"""
    return trading_service.metrics

@app.get("/autonomy")
async def get_autonomy_status():
    """Get autonomy engine status"""
    if autonomy_engine:
        return await autonomy_engine.get_autonomy_status()
    return {"error": "Autonomy engine not initialized"}

@app.post("/trading/start")
async def start_trading():
    """Start autonomous trading"""
    try:
        await trading_service.start_trading()
        return {"message": "Trading started successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trading/stop")
async def stop_trading():
    """Stop autonomous trading"""
    try:
        await trading_service.stop_trading()
        return {"message": "Trading stopped successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agents")
async def get_agents_status():
    """Get agent orchestrator status"""
    if agent_orchestrator:
        return await agent_orchestrator.get_status()
    return {"error": "Agent orchestrator not initialized"}

@app.get("/learning")
async def get_learning_status():
    """Get learning agent status"""
    if learning_agent:
        return await learning_agent.get_status()
    return {"error": "Learning agent not initialized"}

@app.post("/heal")
async def trigger_self_healing():
    """Trigger self-healing process"""
    if self_healing_system:
        await self_healing_system.perform_full_system_check()
        return {"message": "Self-healing process triggered"}
    return {"error": "Self-healing system not initialized"}

@app.get("/models")
async def get_models_status():
    """Get model selector status"""
    if model_selector:
        return await model_selector.get_model_insights()
    return {"error": "Model selector not initialized"}

@app.get("/optimization")
async def get_optimization_status():
    """Get code optimizer status"""
    if code_optimizer:
        return await code_optimizer.get_optimization_status()
    return {"error": "Code optimizer not initialized"}


@app.on_event("startup")
async def startup_event():
    """Initialize the Super-GPT trading bot on startup"""
    try:
        await trading_service.initialize()
        # Start trading automatically
        await trading_service.start_trading()
    except Exception as e:
        logging.error(f"Failed to start Super-GPT trading bot: {e}")
        sys.exit(1)


@app.on_event("shutdown")
async def shutdown_event():
    """Gracefully shutdown the Super-GPT trading bot"""
    await trading_service.stop_trading()


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logging.info(f"Received signal {signum}, shutting down Super-GPT trading bot...")
    global is_shutting_down
    is_shutting_down = True


def main():
    """Main entry point"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Run the FastAPI app
    config = BotConfig()
    
    print("🚀 Starting Super-GPT Autonomous Trading Bot")
    print(f"🤖 Version: 3.0.0")
    print(f"📊 Trading pairs: {', '.join(config.get_trading_pairs())}")
    print(f"🔧 API server: http://{config.api_host}:{config.api_port}")
    print(f"📈 Paper trading: {'Enabled' if config.paper_trading else 'Disabled'}")
    print(f"🧠 AI Features: Multi-agent, Self-healing, Meta-learning, Code optimization")
    print(f"⚡ Autonomy Level: Full autonomous operation")
    print("=" * 80)
    
    uvicorn.run(
        "main:app",
        host=config.api_host,
        port=config.api_port,
        reload=config.debug_mode,
        log_level="info"
    )


if __name__ == "__main__":
    main()
