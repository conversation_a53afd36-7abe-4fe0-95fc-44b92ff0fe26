@echo orem Initialize PRODUCTION database for REAL TRADING
echo Initializing PRODUCTION database for REAL MONEY trading...
C:/Users/<USER>/miniconda3/Scripts/conda.exe run -p C:\Users\<USER>\.conda\envs\bybit-trader --no-capture-output python database_init_production.py

rem Start the Super-GPT PRODUCTION trading bot
echo Starting Super-GPT PRODUCTION system...
C:/Users/<USER>/miniconda3/Scripts/conda.exe run -p C:\Users\<USER>\.conda\envs\bybit-trader --no-capture-output python main_supergpt_simple.pyo Starting Super-GPT Autonomous Trading Bot...
echo.

rem Change to the bot directory
cd /d "e:\The_real_deal_copy\Bybit_Bot\BOT"

rem Initialize database if needed
echo Initializing database...
C:/Users/<USER>/miniconda3/Scripts/conda.exe run -p C:\Users\<USER>\.conda\envs\bybit-trader --no-capture-output python database_init_standalone.py

rem Start the Super-GPT trading bot
echo Starting Super-GPT system...
C:/Users/<USER>/miniconda3/Scripts/conda.exe run -p C:\Users\<USER>\.conda\envs\bybit-trader --no-capture-output python main_supergpt.py

pause
