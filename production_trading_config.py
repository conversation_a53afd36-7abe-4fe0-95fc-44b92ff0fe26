#!/usr/bin/env python3
"""
Production Trading Configuration for Maximum Profit
REAL MONEY TRADING - AGGRESSIVE PROFIT OPTIMIZATION
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass, asdict

logger = logging.getLogger("PRODUCTION_TRADING_CONFIG")

@dataclass
class ProductionRiskConfig:
    """Production risk configuration optimized for profit maximization"""
    # Aggressive position sizing for maximum profit
    max_position_size: float = 0.50          # 50% of account per position
    max_daily_loss: float = 0.15             # 15% max daily loss
    max_drawdown: float = 0.25               # 25% max drawdown
    
    # Optimized stop-loss and take-profit
    stop_loss_percentage: float = 0.03       # 3% stop loss (tight)
    take_profit_percentage: float = 0.08     # 8% take profit (aggressive)
    trailing_stop_percentage: float = 0.02   # 2% trailing stop
    
    # Risk per trade optimization
    risk_per_trade: float = 0.05             # 5% risk per trade (aggressive)
    max_risk_per_trade: float = 0.08         # 8% maximum risk per trade
    
    # Leverage settings for profit maximization
    max_leverage: int = 20                   # Higher leverage for profits
    default_leverage: int = 5                # Default leverage
    leverage_range: List[int] = None
    
    # Portfolio risk management
    max_correlation: float = 0.85            # Allow higher correlation
    var_confidence: float = 0.95             # 95% VaR confidence
    emergency_stop_loss: float = 0.20        # 20% emergency stop
    
    # Advanced risk parameters
    volatility_adjustment: bool = True        # Adjust for volatility
    sentiment_adjustment: bool = True         # Adjust for sentiment
    momentum_adjustment: bool = True          # Adjust for momentum
    
    def __post_init__(self):
        if self.leverage_range is None:
            self.leverage_range = [1, self.max_leverage]

@dataclass
class ProductionTradingConfig:
    """Production trading configuration for real money"""
    # Core trading settings
    enabled: bool = True
    environment: str = "production"
    paper_trading: bool = False              # DISABLED
    live_trading: bool = True                # ENABLED
    real_money_mode: bool = True
    
    # Trading pairs for maximum opportunities
    trading_pairs: List[str] = None
    
    # Execution settings optimized for speed
    order_timeout: int = 15                  # Fast execution
    slippage_tolerance: float = 0.003        # Tight slippage
    trading_cycle_interval: int = 5          # 5 seconds for real-time
    max_concurrent_orders: int = 15          # More concurrent orders
    order_retry_attempts: int = 3
    position_update_interval: int = 3        # 3 seconds updates
    
    # Strategy weights optimized for profit
    strategy_weights: Dict[str, float] = None
    
    # Advanced execution features
    smart_order_routing: bool = True
    iceberg_orders: bool = True
    time_weighted_execution: bool = True
    market_impact_minimization: bool = True
    
    def __post_init__(self):
        if self.trading_pairs is None:
            self.trading_pairs = [
                "BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT",
                "BNBUSDT", "XRPUSDT", "MATICUSDT", "AVAXUSDT", "LINKUSDT",
                "UNIUSDT", "LTCUSDT", "ATOMUSDT", "FILUSDT", "AAVEUSDT"
            ]
        
        if self.strategy_weights is None:
            self.strategy_weights = {
                "momentum": 0.25,
                "mean_reversion": 0.20,
                "trend_following": 0.25,
                "arbitrage": 0.15,
                "scalping": 0.10,
                "breakout": 0.05
            }

@dataclass
class ProductionStrategyConfig:
    """Production strategy configuration"""
    # Momentum strategy - aggressive
    momentum: Dict[str, Any] = None
    
    # Mean reversion strategy - optimized
    mean_reversion: Dict[str, Any] = None
    
    # Trend following strategy - enhanced
    trend_following: Dict[str, Any] = None
    
    # Arbitrage strategy - profit focused
    arbitrage: Dict[str, Any] = None
    
    # Scalping strategy - high frequency
    scalping: Dict[str, Any] = None
    
    # Breakout strategy - momentum capture
    breakout: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.momentum is None:
            self.momentum = {
                "enabled": True,
                "weight": 0.25,
                "lookback_period": 14,
                "rsi_threshold": [20, 80],      # More aggressive
                "momentum_threshold": 0.02,
                "volume_confirmation": True
            }
        
        if self.mean_reversion is None:
            self.mean_reversion = {
                "enabled": True,
                "weight": 0.20,
                "bollinger_period": 20,
                "bollinger_std": 2.0,
                "oversold_threshold": 25,       # More aggressive
                "overbought_threshold": 75
            }
        
        if self.trend_following is None:
            self.trend_following = {
                "enabled": True,
                "weight": 0.25,
                "ema_short": 8,                 # Faster signals
                "ema_long": 21,
                "signal_period": 9,
                "trend_strength_threshold": 0.6
            }
        
        if self.arbitrage is None:
            self.arbitrage = {
                "enabled": True,
                "weight": 0.15,
                "min_profit_threshold": 0.002,  # 0.2% minimum
                "max_execution_time": 3,        # 3 seconds
                "cross_exchange": True,
                "funding_rate_arbitrage": True
            }
        
        if self.scalping is None:
            self.scalping = {
                "enabled": True,
                "weight": 0.10,
                "timeframe": "1m",
                "profit_target": 0.005,         # 0.5% profit
                "max_holding_time": 300,        # 5 minutes
                "volume_spike_threshold": 2.0
            }
        
        if self.breakout is None:
            self.breakout = {
                "enabled": True,
                "weight": 0.05,
                "volatility_threshold": 0.02,
                "volume_confirmation": True,
                "false_breakout_filter": True,
                "momentum_confirmation": True
            }

class ProductionTradingConfigManager:
    """Manages production trading configuration"""
    
    def __init__(self):
        self.logger = logging.getLogger("ProductionTradingConfig")
        self.config_path = Path("config_production_trading.yaml")
        
        # Initialize configurations
        self.risk_config = ProductionRiskConfig()
        self.trading_config = ProductionTradingConfig()
        self.strategy_config = ProductionStrategyConfig()
    
    def create_production_config(self) -> Dict[str, Any]:
        """Create complete production trading configuration"""
        config = {
            "system": {
                "name": "Autonomous Bybit Trading Bot - Production",
                "version": "2.0.0",
                "environment": "production",
                "mode": "live_trading",
                "debug_mode": False,
                "auto_start": True,
                "self_healing": True,
                "autonomous_mode": True,
                "profit_maximization": True
            },
            
            "trading": asdict(self.trading_config),
            "risk": asdict(self.risk_config),
            "strategies": asdict(self.strategy_config),
            
            "api_keys": {
                "bybit": {
                    "api_key": os.getenv("BYBIT_API_KEY", "WbQDRvmESPfUGgXQEj"),
                    "api_secret": os.getenv("BYBIT_API_SECRET", "vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga"),
                    "testnet": False,
                    "base_url": "https://api.bybit.com",
                    "websocket_url": "wss://stream.bybit.com/v5/public/linear",
                    "recv_window": 5000
                }
            },
            
            "database": {
                "type": "sqlite",
                "url": "sqlite:///E:/bybit_bot_data/bybit_trading_bot_production.db",
                "path": "E:/bybit_bot_data/bybit_trading_bot_production.db"
            },
            
            "logging": {
                "level": "INFO",
                "file": "E:/bybit_bot_logs/trading_bot_production.log",
                "max_file_size": "50MB",
                "backup_count": 10
            },
            
            "performance": {
                "optimization_enabled": True,
                "profit_tracking": True,
                "real_time_analytics": True,
                "performance_alerts": True
            }
        }
        
        return config
    
    def save_production_config(self):
        """Save production configuration to file"""
        try:
            config = self.create_production_config()
            
            with open(self.config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            self.logger.info(f"✅ Production trading configuration saved to {self.config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to save production config: {e}")
            return False
    
    def validate_production_config(self) -> List[str]:
        """Validate production configuration"""
        errors = []
        
        # Validate risk parameters
        if self.risk_config.max_position_size > 1.0:
            errors.append("max_position_size cannot exceed 100%")
        
        if self.risk_config.stop_loss_percentage >= self.risk_config.take_profit_percentage:
            errors.append("stop_loss_percentage must be less than take_profit_percentage")
        
        # Validate trading configuration
        if not self.trading_config.trading_pairs:
            errors.append("trading_pairs cannot be empty")
        
        if self.trading_config.paper_trading:
            errors.append("paper_trading must be disabled for production")
        
        # Validate API keys
        if not os.getenv("BYBIT_API_KEY") or os.getenv("BYBIT_API_KEY") == "YOUR_BYBIT_API_KEY":
            errors.append("Valid Bybit API key required")
        
        return errors

def main():
    """Main function to create and save production trading configuration"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("PRODUCTION_CONFIG_SETUP")
    
    logger.info("🚀 CREATING PRODUCTION TRADING CONFIGURATION...")
    logger.info("💰 OPTIMIZED FOR MAXIMUM PROFIT")
    logger.info("🚫 NO SIMULATIONS - REAL MONEY ONLY")
    
    config_manager = ProductionTradingConfigManager()
    
    # Validate configuration
    errors = config_manager.validate_production_config()
    if errors:
        logger.error(f"❌ Configuration validation errors: {errors}")
        return False
    
    # Save configuration
    success = config_manager.save_production_config()
    
    if success:
        logger.info("✅ PRODUCTION TRADING CONFIGURATION CREATED!")
        logger.info("🎯 READY FOR AGGRESSIVE PROFIT TRADING!")
        logger.info("📈 MAXIMUM PROFIT OPTIMIZATION ENABLED!")
    else:
        logger.error("❌ FAILED TO CREATE PRODUCTION CONFIGURATION!")
    
    return success

if __name__ == "__main__":
    main()
