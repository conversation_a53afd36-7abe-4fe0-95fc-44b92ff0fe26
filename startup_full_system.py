#!/usr/bin/env python3
"""
Full System Startup Script
Activates all features including SuperGPT functions
"""

import asyncio
import logging
import sys
import signal
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from bybit_bot.core.config import get_config, EnhancedBotConfig
from bybit_bot.core.logging_setup import setup_logging
from bybit_bot.core.database import DatabaseManager
from bybit_bot.trading.exchange_manager import ExchangeManager
from bybit_bot.ai.supergpt_engine import SuperGPTEngine
from bybit_bot.data.crawler_manager import DataCrawlerManager
from bybit_bot.monitoring.system_monitor import SystemMonitor
from bybit_bot.api.server import APIServer

logger = logging.getLogger(__name__)

class FullSystemManager:
    """Manages the complete autonomous trading system with all features"""
    
    def __init__(self):
        self.config: EnhancedBotConfig = get_config()
        self.components: Dict[str, Any] = {}
        self.running = False
        self.startup_complete = False
        
    async def initialize_system(self):
        """Initialize all system components"""
        logger.info("🚀 Starting Autonomous Bybit Trading Bot - Full System")
        logger.info(f"System: {self.config.system.name} v{self.config.system.version}")
        
        try:
            # 1. Setup logging
            await self._setup_logging()
            
            # 2. Initialize database
            await self._initialize_database()
            
            # 3. Initialize SuperGPT engine
            await self._initialize_supergpt()
            
            # 4. Initialize data crawler
            await self._initialize_data_crawler()
            
            # 5. Initialize exchange manager
            await self._initialize_exchange_manager()
            
            # 6. Initialize system monitor
            await self._initialize_system_monitor()
            
            # 7. Initialize API server
            await self._initialize_api_server()
            
            # 8. Enable all features
            await self._enable_all_features()
            
            # 9. Start all components
            await self._start_all_components()
            
            self.startup_complete = True
            logger.info("✅ Full system startup complete - All features active")
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            await self.shutdown()
            raise
    
    async def _setup_logging(self):
        """Setup comprehensive logging"""
        logger.info("📝 Setting up logging system...")
        setup_logging(self.config.logging_config)
        logger.info("✅ Logging system initialized")
    
    async def _initialize_database(self):
        """Initialize database connections"""
        logger.info("🗄️ Initializing database connections...")
        self.components['database'] = DatabaseManager(self.config.database)
        await self.components['database'].initialize()
        logger.info("✅ Database connections established")
    
    async def _initialize_supergpt(self):
        """Initialize SuperGPT engine"""
        if self.config.supergpt.enabled:
            logger.info("🧠 Initializing SuperGPT engine...")
            self.components['supergpt'] = SuperGPTEngine(self.config)
            await self.components['supergpt'].initialize()
            logger.info("✅ SuperGPT engine initialized with all capabilities")
        else:
            logger.warning("⚠️ SuperGPT engine disabled in configuration")
    
    async def _initialize_data_crawler(self):
        """Initialize data crawler"""
        logger.info("📊 Initializing data crawler...")
        self.components['data_crawler'] = DataCrawlerManager(self.config)
        await self.components['data_crawler'].initialize()
        logger.info("✅ Data crawler initialized")
    
    async def _initialize_exchange_manager(self):
        """Initialize exchange manager"""
        if self.config.trading.enabled:
            logger.info("💱 Initializing exchange manager...")
            self.components['exchange'] = ExchangeManager(self.config)
            await self.components['exchange'].initialize()
            logger.info("✅ Exchange manager initialized")
        else:
            logger.warning("⚠️ Trading disabled in configuration")
    
    async def _initialize_system_monitor(self):
        """Initialize system monitor"""
        logger.info("📈 Initializing system monitor...")
        self.components['monitor'] = SystemMonitor(self.config)
        await self.components['monitor'].initialize()
        logger.info("✅ System monitor initialized")
    
    async def _initialize_api_server(self):
        """Initialize API server"""
        if self.config.api.get('enabled', True):
            logger.info("🌐 Initializing API server...")
            self.components['api_server'] = APIServer(self.config)
            await self.components['api_server'].initialize()
            logger.info("✅ API server initialized")
    
    async def _enable_all_features(self):
        """Enable all available features"""
        logger.info("🔧 Enabling all features...")
        
        # Enable SuperGPT features
        if 'supergpt' in self.components:
            await self.components['supergpt'].enable_all_capabilities()
        
        # Enable trading strategies
        if 'exchange' in self.components:
            await self.components['exchange'].enable_all_strategies()
        
        # Enable data sources
        if 'data_crawler' in self.components:
            await self.components['data_crawler'].enable_all_sources()
        
        logger.info("✅ All features enabled")
    
    async def _start_all_components(self):
        """Start all initialized components"""
        logger.info("▶️ Starting all components...")
        
        start_order = [
            'database',
            'supergpt',
            'data_crawler',
            'exchange',
            'monitor',
            'api_server'
        ]
        
        for component_name in start_order:
            if component_name in self.components:
                try:
                    await self.components[component_name].start()
                    logger.info(f"✅ {component_name} started")
                except Exception as e:
                    logger.error(f"❌ Failed to start {component_name}: {e}")
                    raise
        
        self.running = True
        logger.info("✅ All components started successfully")
    
    async def run_forever(self):
        """Run the system forever"""
        logger.info("🔄 System running in autonomous mode...")
        
        try:
            while self.running:
                # System health check
                if 'monitor' in self.components:
                    await self.components['monitor'].health_check()
                
                # SuperGPT continuous learning
                if 'supergpt' in self.components and self.config.supergpt.auto_learning:
                    await self.components['supergpt'].continuous_learning_cycle()
                
                # Trading cycle
                if 'exchange' in self.components and self.config.trading.enabled:
                    await self.components['exchange'].trading_cycle()
                
                # Data update cycle
                if 'data_crawler' in self.components:
                    await self.components['data_crawler'].update_cycle()
                
                # Wait for next cycle
                await asyncio.sleep(self.config.trading.execution.get('trading_cycle_interval', 5))
                
        except KeyboardInterrupt:
            logger.info("🛑 Received shutdown signal")
        except Exception as e:
            logger.error(f"❌ System error: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Graceful shutdown of all components"""
        logger.info("🛑 Shutting down system...")
        self.running = False
        
        # Shutdown in reverse order
        shutdown_order = [
            'api_server',
            'monitor',
            'exchange',
            'data_crawler',
            'supergpt',
            'database'
        ]
        
        for component_name in shutdown_order:
            if component_name in self.components:
                try:
                    await self.components[component_name].shutdown()
                    logger.info(f"✅ {component_name} shut down")
                except Exception as e:
                    logger.error(f"❌ Error shutting down {component_name}: {e}")
        
        logger.info("✅ System shutdown complete")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}")
        asyncio.create_task(self.shutdown())

async def main():
    """Main entry point"""
    try:
        # Initialize system
        system = FullSystemManager()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, system.signal_handler)
        signal.signal(signal.SIGTERM, system.signal_handler)
        
        # Initialize and start system
        await system.initialize_system()
        
        # Run forever
        await system.run_forever()
        
    except Exception as e:
        logger.error(f"❌ System startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 Autonomous Bybit Trading Bot - Full System Startup")
    print("=" * 60)
    print("Features:")
    print("✅ SuperGPT Functions")
    print("✅ Autonomous Trading")
    print("✅ Real-time Data")
    print("✅ AI/ML Engine")
    print("✅ Risk Management")
    print("✅ Performance Monitoring")
    print("✅ System Health")
    print("✅ API Server")
    print("=" * 60)
    
    # Run the system
    asyncio.run(main())
